# GameFlex Backend Development Setup Script (PowerShell)
# This script starts the Supabase development environment

param(
    [switch]$Help
)

if ($Help) {
    Write-Host "GameFlex Backend Development Setup" -ForegroundColor Green
    Write-Host "Usage: .\start.ps1" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "This script will start the GameFlex development backend using Docker Compose."
    Write-Host "Make sure Docker Desktop is running before executing this script."
    exit 0
}

# Set error action preference
$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting GameFlex Development Backend..." -ForegroundColor Green

# Check if Docker is running
try {
    docker info | Out-Null
    Write-Host "✅ Docker is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop and try again." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is available
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not available. Please install Docker Desktop with Compose support." -ForegroundColor Red
    exit 1
}

# Create necessary directories
Write-Host "📁 Creating necessary directories..." -ForegroundColor Yellow
$directories = @(
    "volumes\storage",
    "volumes\functions"
)

foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "   Created: $dir" -ForegroundColor Gray
    }
}

# Check if .env file exists
if (!(Test-Path ".env")) {
    Write-Host "❌ .env file not found. Please create one based on the provided template." -ForegroundColor Red
    exit 1
}

Write-Host "📋 Environment file found" -ForegroundColor Green

# Check if hosts file is configured for domain names
Write-Host "🔍 Checking hosts file configuration..." -ForegroundColor Yellow
$hostsFile = "$env:SystemRoot\System32\drivers\etc\hosts"
$hostsContent = Get-Content $hostsFile -ErrorAction SilentlyContinue
if ($hostsContent -notcontains "# GameFlex Development - START") {
    Write-Host "⚠️  GameFlex domain names not configured in hosts file!" -ForegroundColor Yellow
    Write-Host "   To enable domain names (api.gameflex.local, studio.gameflex.local, etc.):" -ForegroundColor Gray
    Write-Host "   Run as Administrator: .\setup-hosts-windows.ps1" -ForegroundColor White
    Write-Host "   Or continue with localhost URLs..." -ForegroundColor Gray
    Write-Host ""
} else {
    Write-Host "✅ GameFlex domain names configured" -ForegroundColor Green
}

# Load environment variables from .env file
$envVars = @{}
Get-Content ".env" | ForEach-Object {
    if ($_ -match "^([^#][^=]+)=(.*)$") {
        $envVars[$matches[1]] = $matches[2]
    }
}

# Set default ports if not specified
$STUDIO_PORT = if ($envVars["STUDIO_PORT"]) { $envVars["STUDIO_PORT"] } else { "3000" }
$KONG_HTTP_PORT = if ($envVars["KONG_HTTP_PORT"]) { $envVars["KONG_HTTP_PORT"] } else { "8000" }
$POSTGRES_PORT = if ($envVars["POSTGRES_PORT"]) { $envVars["POSTGRES_PORT"] } else { "5432" }

# Start the services
Write-Host "🐳 Starting Docker containers..." -ForegroundColor Yellow
try {
    # Stop any existing containers first
    docker-compose down 2>$null | Out-Null

    # Start containers with better error handling
    $result = docker-compose up -d 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Failed to start Docker containers:" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
        Write-Host ""
        Write-Host "🔍 Checking for common issues..." -ForegroundColor Yellow

        # Check if it's a port binding issue
        if ($result -match "bind.*permission") {
            Write-Host "   Port binding permission issue detected." -ForegroundColor Yellow
            Write-Host "   Try running Docker Desktop as Administrator or check Windows port exclusions." -ForegroundColor Gray
        }

        # Show container status
        Write-Host "📊 Container status:" -ForegroundColor Cyan
        docker-compose ps
        exit 1
    }
    Write-Host "✅ Docker containers started" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to start Docker containers. Check the logs for details." -ForegroundColor Red
    docker-compose logs --tail=50
    exit 1
}

# Wait for services to be ready
Write-Host "⏳ Waiting for services to be ready..." -ForegroundColor Yellow
Start-Sleep -Seconds 15

# Function to check if a URL is responding
function Test-Url {
    param([string]$Url, [int]$TimeoutSeconds = 5)
    try {
        $response = Invoke-WebRequest -Uri $Url -TimeoutSec $TimeoutSeconds -UseBasicParsing -ErrorAction SilentlyContinue
        return $response.StatusCode -eq 200
    } catch {
        return $false
    }
}

# Function to check database connectivity
function Test-Database {
    try {
        # First check if the container is running
        $dbStatus = docker-compose ps db --format json 2>$null
        if (-not $dbStatus) {
            return $false
        }

        $dbJson = $dbStatus | ConvertFrom-Json -ErrorAction SilentlyContinue
        if (-not $dbJson -or $dbJson.State -ne "running") {
            return $false
        }

        # Then check if PostgreSQL is ready
        $result = docker-compose exec -T db pg_isready -U postgres -h localhost 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

# Check if database is ready
Write-Host "🔍 Checking database health..." -ForegroundColor Yellow
$dbReady = $false
$attempts = 0
$maxAttempts = 15

while (-not $dbReady -and $attempts -lt $maxAttempts) {
    $dbReady = Test-Database
    if (-not $dbReady) {
        Write-Host "   Waiting for database... (attempt $($attempts + 1)/$maxAttempts)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempts++
    }
}

if ($dbReady) {
    Write-Host "✅ Database is ready!" -ForegroundColor Green
} else {
    Write-Host "❌ Database failed to start within expected time" -ForegroundColor Red
    Write-Host "🔍 Checking database container status..." -ForegroundColor Yellow

    # Show container status
    $dbContainer = docker-compose ps db --format json 2>$null | ConvertFrom-Json
    if ($dbContainer) {
        Write-Host "   Container State: $($dbContainer.State)" -ForegroundColor Gray
        Write-Host "   Container Status: $($dbContainer.Status)" -ForegroundColor Gray
    }

    Write-Host "📋 Recent database logs:" -ForegroundColor Cyan
    docker-compose logs --tail=20 db
    Write-Host ""
    Write-Host "💡 Try running: docker-compose logs db" -ForegroundColor Yellow
}

# Function to check Kong API Gateway
function Test-Kong {
    try {
        # Check if Kong container is healthy using docker-compose ps
        $kongStatus = docker-compose ps kong --format "table {{.State}}" 2>$null
        if ($kongStatus -match "running") {
            # If container is running, try a simple HTTP request
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$KONG_HTTP_PORT/" -TimeoutSec 5 -UseBasicParsing -ErrorAction Stop
                return $true
            } catch [System.Net.WebException] {
                # HTTP errors (like 401 Unauthorized) mean Kong is responding
                if ($_.Exception.Response.StatusCode) {
                    return $true
                }
                return $false
            } catch [System.Net.Http.HttpRequestException] {
                # HTTP errors mean Kong is responding
                return $true
            } catch {
                return $false
            }
        }
        return $false
    } catch {
        return $false
    }
}

# Check if Kong is ready
Write-Host "🔍 Checking API Gateway health..." -ForegroundColor Yellow
$kongReady = $false
$attempts = 0

while (-not $kongReady -and $attempts -lt $maxAttempts) {
    $kongReady = Test-Kong
    if (-not $kongReady) {
        Write-Host "   Waiting for API Gateway... (attempt $($attempts + 1)/$maxAttempts)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempts++
    }
}

if ($kongReady) {
    Write-Host "✅ API Gateway is ready!" -ForegroundColor Green
} else {
    # Check if container is at least running
    $kongStatus = docker-compose ps kong --format "table {{.State}}" 2>$null
    if ($kongStatus -match "running") {
        Write-Host "✅ API Gateway container is running (may still be initializing)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  API Gateway is not running" -ForegroundColor Yellow
        Write-Host "   Check logs with: docker-compose logs kong" -ForegroundColor Gray
    }
}

# Function to check Studio
function Test-Studio {
    try {
        # Check if Studio container is running
        $studioStatus = docker-compose ps studio --format "table {{.State}}" 2>$null
        if ($studioStatus -match "running") {
            # If container is running, try a simple HTTP request
            try {
                $response = Invoke-WebRequest -Uri "http://localhost:$STUDIO_PORT/" -TimeoutSec 3 -UseBasicParsing -ErrorAction Stop
                return $response.Content -match "<!DOCTYPE html"
            } catch {
                return $false
            }
        }
        return $false
    } catch {
        return $false
    }
}

# Check if Studio is ready
Write-Host "🔍 Checking Supabase Studio health..." -ForegroundColor Yellow
$studioReady = $false
$attempts = 0

while (-not $studioReady -and $attempts -lt $maxAttempts) {
    $studioReady = Test-Studio
    if (-not $studioReady) {
        Write-Host "   Waiting for Supabase Studio... (attempt $($attempts + 1)/$maxAttempts)" -ForegroundColor Gray
        Start-Sleep -Seconds 2
        $attempts++
    }
}

if ($studioReady) {
    Write-Host "✅ Supabase Studio is ready!" -ForegroundColor Green
} else {
    # Check if container is at least running
    $studioStatus = docker-compose ps studio --format "table {{.State}}" 2>$null
    if ($studioStatus -match "running") {
        Write-Host "✅ Supabase Studio container is running (may still be initializing)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Supabase Studio is not running" -ForegroundColor Yellow
        Write-Host "   Check logs with: docker-compose logs studio" -ForegroundColor Gray
    }
}

# Function to check storage initialization
function Test-StorageInit {
    try {
        $storageInitStatus = docker-compose ps storage-init --format json 2>$null
        if (-not $storageInitStatus) {
            return "not_found"
        }

        $storageJson = $storageInitStatus | ConvertFrom-Json -ErrorAction SilentlyContinue
        if (-not $storageJson) {
            return "unknown"
        }

        if ($storageJson.State -eq "exited") {
            if ($storageJson.Status -match "Exited \(0\)") {
                return "success"
            } else {
                return "failed"
            }
        } else {
            return "running"
        }
    } catch {
        return "error"
    }
}

# Check storage initialization
Write-Host "🔍 Checking storage initialization..." -ForegroundColor Yellow
$storageInitResult = Test-StorageInit

switch ($storageInitResult) {
    "success" {
        Write-Host "✅ Storage initialization completed successfully!" -ForegroundColor Green
    }
    "failed" {
        Write-Host "⚠️  Storage initialization failed. Check logs with: docker-compose logs storage-init" -ForegroundColor Yellow
    }
    "running" {
        Write-Host "🔄 Storage initialization is still running..." -ForegroundColor Yellow
        Write-Host "   Waiting for completion..." -ForegroundColor Gray
        Start-Sleep -Seconds 10
        $storageInitResult = Test-StorageInit
        if ($storageInitResult -eq "success") {
            Write-Host "✅ Storage initialization completed successfully!" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Storage initialization taking longer than expected" -ForegroundColor Yellow
        }
    }
    "not_found" {
        Write-Host "ℹ️  Storage initialization container not found (may not be configured)" -ForegroundColor Gray
    }
    default {
        Write-Host "⚠️  Could not check storage initialization status" -ForegroundColor Yellow
    }
}

# Initialize auth users
Write-Host "🔐 Initializing authentication users..." -ForegroundColor Yellow
try {
    if (Test-Path "scripts\init-auth-users.ps1") {
        $authResult = & ".\scripts\init-auth-users.ps1" 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Authentication users initialized successfully!" -ForegroundColor Green
        } else {
            Write-Host "⚠️  Authentication users initialization completed with warnings" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  Authentication users initialization script not found" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Authentication users initialization may have failed" -ForegroundColor Yellow
    Write-Host "   You can run it manually later: .\scripts\init-auth-users.ps1" -ForegroundColor Gray
}

# Final service status check
Write-Host "🔍 Final service status check..." -ForegroundColor Yellow
try {
    $allContainers = docker-compose ps --format json 2>$null | ConvertFrom-Json
    $runningCount = 0
    $totalCount = 0

    if ($allContainers) {
        if ($allContainers -is [array]) {
            $totalCount = $allContainers.Count
            $runningCount = ($allContainers | Where-Object { $_.State -eq "running" }).Count
        } else {
            $totalCount = 1
            $runningCount = if ($allContainers.State -eq "running") { 1 } else { 0 }
        }
    }

    if ($runningCount -eq $totalCount -and $totalCount -gt 0) {
        Write-Host "✅ All $totalCount services are running!" -ForegroundColor Green
    } elseif ($runningCount -gt 0) {
        Write-Host "⚠️  $runningCount of $totalCount services are running" -ForegroundColor Yellow
    } else {
        Write-Host "❌ No services are running" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️  Could not check final service status" -ForegroundColor Yellow
}

# Display success message and information
Write-Host ""
Write-Host "🎉 GameFlex Development Backend is now running!" -ForegroundColor Green -BackgroundColor DarkGreen
Write-Host ""

# Check if domain names are configured and show appropriate URLs
$hostsContent = Get-Content "$env:SystemRoot\System32\drivers\etc\hosts" -ErrorAction SilentlyContinue
if ($hostsContent -contains "# GameFlex Development - START") {
    Write-Host "🎯 GameFlex Services (Domain Names):" -ForegroundColor Cyan
    Write-Host "   📊 Supabase Studio: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://studio.gameflex.local:$STUDIO_PORT" -ForegroundColor White
    Write-Host "   🔌 API Gateway: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://api.gameflex.local:$KONG_HTTP_PORT" -ForegroundColor White
    Write-Host "   🗄️  Database: " -NoNewline -ForegroundColor Cyan
    Write-Host "db.gameflex.local:$POSTGRES_PORT" -ForegroundColor White
} else {
    Write-Host "🎯 GameFlex Services (Localhost):" -ForegroundColor Cyan
    Write-Host "   📊 Supabase Studio: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://localhost:$STUDIO_PORT" -ForegroundColor White
    Write-Host "   🔌 API Gateway: " -NoNewline -ForegroundColor Cyan
    Write-Host "http://localhost:$KONG_HTTP_PORT" -ForegroundColor White
    Write-Host "   🗄️  Database: " -NoNewline -ForegroundColor Cyan
    Write-Host "localhost:$POSTGRES_PORT" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 To enable domain names, run as Administrator:" -ForegroundColor Yellow
    Write-Host "   .\setup-hosts-windows.ps1" -ForegroundColor White
}
Write-Host ""
Write-Host "🔑 Development Credentials:" -ForegroundColor Yellow
Write-Host "   📧 <EMAIL> (password: devpassword123)" -ForegroundColor White
Write-Host "   📧 <EMAIL> (password: adminpassword123)" -ForegroundColor White
Write-Host "   📧 <EMAIL> (password: johnpassword123)" -ForegroundColor White
Write-Host "   📧 <EMAIL> (password: janepassword123)" -ForegroundColor White
Write-Host "   📧 <EMAIL> (password: mikepassword123)" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Management Commands:" -ForegroundColor Magenta
Write-Host "   To stop the backend: " -NoNewline -ForegroundColor Gray
Write-Host ".\stop.ps1 -KeepData" -ForegroundColor White
Write-Host "   To view logs: " -NoNewline -ForegroundColor Gray
Write-Host "docker-compose logs -f" -ForegroundColor White
Write-Host "   To reset data: " -NoNewline -ForegroundColor Gray
Write-Host ".\stop.ps1; .\start.ps1" -ForegroundColor White
Write-Host "   To test storage: " -NoNewline -ForegroundColor Gray
Write-Host ".\scripts\test-storage.ps1" -ForegroundColor White
Write-Host ""

# Open browser to Studio (optional)
$openBrowser = Read-Host "Would you like to open Supabase Studio in your browser? (y/N)"
if ($openBrowser -eq "y" -or $openBrowser -eq "Y") {
    Start-Process "http://localhost:$STUDIO_PORT"
}
