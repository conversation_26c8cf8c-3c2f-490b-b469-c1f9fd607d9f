import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/theme/app_theme.dart';
import 'package:gameflex_mobile/widgets/common/gf_button.dart';
import 'package:gameflex_mobile/screens/profile_screen.dart';
import 'package:gameflex_mobile/providers/posts_provider.dart';
import 'package:gameflex_mobile/widgets/feed.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  late final Widget _feedWidget;

  @override
  void initState() {
    super.initState();
    // Initialize the feed widget once to preserve state
    _feedWidget = const Feed();

    // Load posts when the home screen is first created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final postsProvider = Provider.of<PostsProvider>(context, listen: false);
      if (postsProvider.status == PostsStatus.initial) {
        postsProvider.loadPosts();
      }
      // Start real-time subscriptions
      postsProvider.startRealtimeSubscriptions();
    });
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          _selectedIndex == 0
              ? null
              : AppBar(
                title: Text(
                  _getTabTitle(),
                  style: const TextStyle(
                    color: AppColors.gfGreen,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.search, color: AppColors.gfOffWhite),
                    onPressed: () {},
                  ),
                ],
              ),
      body: _buildBody(),
      bottomNavigationBar:
          _selectedIndex == 0
              ? null
              : BottomNavigationBar(
                type: BottomNavigationBarType.fixed,
                currentIndex: _selectedIndex,
                onTap: _onItemTapped,
                items: const [
                  BottomNavigationBarItem(
                    icon: Icon(Icons.home),
                    label: 'Home',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.notifications),
                    label: 'Alerts',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.add_circle_outline),
                    label: 'Create',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.person),
                    label: 'Profile',
                  ),
                ],
              ),
    );
  }

  String _getTabTitle() {
    switch (_selectedIndex) {
      case 1:
        return 'Alerts';
      case 2:
        return 'Create';
      case 3:
        return 'Profile';
      default:
        return 'GameFlex';
    }
  }

  Widget _buildBody() {
    switch (_selectedIndex) {
      case 0:
        return _buildHomeTab();
      case 1:
        return _buildPlaceholderTab('Alerts');
      case 2:
        return _buildPlaceholderTab('Create');
      case 3:
        return const ProfileScreen();
      default:
        return _buildHomeTab();
    }
  }

  Widget _buildHomeTab() {
    return Stack(
      children: [
        _feedWidget,
        // Floating navigation overlay
        Positioned(
          bottom: 30,
          left: 0,
          right: 0,
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 179), // 0.7 opacity
                borderRadius: BorderRadius.circular(25),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildNavButton(Icons.notifications, 1),
                  const SizedBox(width: 20),
                  _buildNavButton(Icons.add_circle_outline, 2),
                  const SizedBox(width: 20),
                  _buildNavButton(Icons.person, 3),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNavButton(IconData icon, int index) {
    return GestureDetector(
      onTap: () => _onItemTapped(index),
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Icon(icon, color: Colors.white, size: 24),
      ),
    );
  }

  Widget _buildPlaceholderTab(String tabName) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            tabName,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.gfOffWhite,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'This is a placeholder for the tab content',
            style: TextStyle(color: AppColors.gfGrayText),
          ),
          const SizedBox(height: 24),
          GFButton(
            text: 'Go to Home',
            onPressed: () => _onItemTapped(0),
            type: GFButtonType.primary,
          ),
        ],
      ),
    );
  }
}
