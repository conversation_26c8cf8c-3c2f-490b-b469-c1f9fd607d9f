import 'package:flutter_test/flutter_test.dart';
import 'package:gameflex_mobile/services/supabase_service.dart';

void main() {
  group('URL Transformation Tests', () {
    test('should transform localhost URLs to ********', () {
      const originalUrl = 'http://localhost:8000/storage/v1/object/public/media/user/123/456/image.jpg';
      const expectedUrl = 'http://********:8000/storage/v1/object/public/media/user/123/456/image.jpg';
      
      final transformedUrl = SupabaseService.transformUrl(originalUrl);
      
      expect(transformedUrl, equals(expectedUrl));
    });

    test('should not transform URLs that do not contain localhost:8000', () {
      const originalUrl = 'http://example.com:8000/storage/v1/object/public/media/user/123/456/image.jpg';
      
      final transformedUrl = SupabaseService.transformUrl(originalUrl);
      
      expect(transformedUrl, equals(originalUrl));
    });

    test('should handle null URLs', () {
      final transformedUrl = SupabaseService.transformUrl(null);
      
      expect(transformedUrl, isNull);
    });

    test('should handle empty URLs', () {
      const originalUrl = '';
      
      final transformedUrl = SupabaseService.transformUrl(originalUrl);
      
      expect(transformedUrl, equals(originalUrl));
    });

    test('should transform multiple localhost occurrences', () {
      const originalUrl = 'http://localhost:8000/api/localhost:8000/test';
      const expectedUrl = 'http://********:8000/api/********:8000/test';
      
      final transformedUrl = SupabaseService.transformUrl(originalUrl);
      
      expect(transformedUrl, equals(expectedUrl));
    });
  });
}
