import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/post_model.dart';
import '../models/like_model.dart';
import 'supabase_service.dart';

class PostsService {
  static PostsService? _instance;
  static PostsService get instance => _instance ??= PostsService._();

  PostsService._();

  SupabaseClient get _client => SupabaseService.instance.client;

  /// Fetch posts with user information
  Future<List<PostModel>> getPosts({int limit = 20, int offset = 0}) async {
    try {
      developer.log(
        'PostsService: Fetching posts with limit=$limit, offset=$offset',
      );

      final currentUser = _client.auth.currentUser;
      final currentUserId = currentUser?.id ?? '';

      // Query posts with user information using a join
      // First get posts with media
      final mediaPostsResponse = await _client
          .from('posts')
          .select('''
            id,
            user_id,
            channel_id,
            content,
            media_url,
            media_type,
            like_count,
            comment_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
          .eq('is_active', true)
          .not('media_url', 'is', null)
          .neq('media_url', '')
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 2); // Leave room for one text post

      // Get one text-only post (no media)
      final textPostResponse = await _client
          .from('posts')
          .select('''
            id,
            user_id,
            channel_id,
            content,
            media_url,
            media_type,
            like_count,
            comment_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
          .eq('is_active', true)
          .or('media_url.is.null,media_url.eq.')
          .order('created_at', ascending: false)
          .limit(1);

      // Combine the results
      final List<dynamic> mediaPosts = mediaPostsResponse as List<dynamic>;
      final List<dynamic> textPosts = textPostResponse as List<dynamic>;
      final List<dynamic> response = [...mediaPosts, ...textPosts];

      developer.log('PostsService: Raw response type: ${response.runtimeType}');
      developer.log('PostsService: Raw response: $response');

      final List<dynamic> data = response;
      developer.log('PostsService: Successfully parsed ${data.length} posts');

      // Get all post IDs to check like status in batch
      final postIds = data.map((json) => json['id'] as String).toList();
      final likedPostIds =
          currentUserId.isNotEmpty
              ? await _getLikedPostIds(currentUserId, postIds)
              : <String>{};

      final posts =
          data.map((json) {
            try {
              // Flatten the user data
              final userInfo = json['users'] as Map<String, dynamic>?;
              final flattenedJson = Map<String, dynamic>.from(json);

              if (userInfo != null) {
                flattenedJson['username'] = userInfo['username'];
                flattenedJson['display_name'] = userInfo['display_name'];
                flattenedJson['avatar_url'] = userInfo['avatar_url'];
              }

              // Add like status for current user
              final postId = json['id'] as String;
              flattenedJson['is_liked_by_current_user'] = likedPostIds.contains(
                postId,
              );

              // Remove the nested users object
              flattenedJson.remove('users');

              developer.log(
                'PostsService: Processing post: ${flattenedJson['id']}',
              );
              return PostModel.fromJson(flattenedJson);
            } catch (e) {
              developer.log('PostsService: Error parsing post: $e');
              developer.log('PostsService: Problematic JSON: $json');
              rethrow;
            }
          }).toList();

      developer.log(
        'PostsService: Successfully created ${posts.length} PostModel objects',
      );
      return posts;
    } catch (e, stackTrace) {
      developer.log(
        'PostsService: Error fetching posts',
        error: e,
        stackTrace: stackTrace,
      );
      if (kDebugMode) {
        print('PostsService ERROR: $e');
        print('PostsService STACK TRACE: $stackTrace');
      }
      throw Exception('Failed to fetch posts: $e');
    }
  }

  /// Fetch posts for a specific user
  Future<List<PostModel>> getUserPosts(
    String userId, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _client
          .from('posts')
          .select('''
            id,
            user_id,
            channel_id,
            content,
            media_url,
            media_type,
            like_count,
            comment_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      final List<dynamic> data = response as List<dynamic>;

      return data.map((json) {
        final userInfo = json['users'] as Map<String, dynamic>?;
        final flattenedJson = Map<String, dynamic>.from(json);

        if (userInfo != null) {
          flattenedJson['username'] = userInfo['username'];
          flattenedJson['display_name'] = userInfo['display_name'];
          flattenedJson['avatar_url'] = userInfo['avatar_url'];
        }

        flattenedJson.remove('users');

        return PostModel.fromJson(flattenedJson);
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch user posts: $e');
    }
  }

  /// Like a post
  Future<bool> likePost(String postId) async {
    try {
      developer.log('PostsService: Starting like operation for post: $postId');

      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        developer.log('PostsService: User not authenticated');
        throw Exception('User not authenticated');
      }

      developer.log('PostsService: Current user ID: ${currentUser.id}');

      // Check if already liked
      developer.log('PostsService: Checking existing like status...');
      final existingLike =
          await _client
              .from('likes')
              .select('id')
              .eq('user_id', currentUser.id)
              .eq('post_id', postId)
              .maybeSingle();

      developer.log('PostsService: Existing like: $existingLike');

      if (existingLike != null) {
        // Unlike the post
        developer.log('PostsService: Unliking post...');
        await _client
            .from('likes')
            .delete()
            .eq('user_id', currentUser.id)
            .eq('post_id', postId);

        // Decrement like count
        developer.log('PostsService: Decrementing like count...');
        await _client.rpc('decrement_post_likes', params: {'post_id': postId});

        developer.log('PostsService: Successfully unliked post');
        return false; // Unliked
      } else {
        // Like the post
        developer.log('PostsService: Liking post...');
        await _client.from('likes').insert({
          'user_id': currentUser.id,
          'post_id': postId,
        });

        // Increment like count
        developer.log('PostsService: Incrementing like count...');
        await _client.rpc('increment_post_likes', params: {'post_id': postId});

        developer.log('PostsService: Successfully liked post');
        return true; // Liked
      }
    } catch (e) {
      developer.log('PostsService: Error in likePost: $e');
      if (kDebugMode) {
        print('PostsService LIKE ERROR: $e');
      }
      throw Exception('Failed to like/unlike post: $e');
    }
  }

  /// Check if current user has liked a post
  Future<bool> hasLikedPost(String postId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) return false;

      final like =
          await _client
              .from('likes')
              .select('id')
              .eq('user_id', currentUser.id)
              .eq('post_id', postId)
              .maybeSingle();

      return like != null;
    } catch (e) {
      return false;
    }
  }

  /// Create a new post
  Future<PostModel?> createPost({
    required String content,
    String? mediaUrl,
    String? mediaType,
    String? channelId,
  }) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      final response =
          await _client
              .from('posts')
              .insert({
                'user_id': currentUser.id,
                'content': content,
                'media_url': mediaUrl,
                'media_type': mediaType,
                'channel_id': channelId,
              })
              .select('''
            id,
            user_id,
            channel_id,
            content,
            media_url,
            media_type,
            like_count,
            comment_count,
            is_active,
            created_at,
            updated_at,
            users!inner(
              username,
              display_name,
              avatar_url
            )
          ''')
              .single();

      final userInfo = response['users'] as Map<String, dynamic>?;
      final flattenedJson = Map<String, dynamic>.from(response);

      if (userInfo != null) {
        flattenedJson['username'] = userInfo['username'];
        flattenedJson['display_name'] = userInfo['display_name'];
        flattenedJson['avatar_url'] = userInfo['avatar_url'];
      }

      flattenedJson.remove('users');

      return PostModel.fromJson(flattenedJson);
    } catch (e) {
      throw Exception('Failed to create post: $e');
    }
  }

  /// Delete a post
  Future<bool> deletePost(String postId) async {
    try {
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      await _client
          .from('posts')
          .update({'is_active': false})
          .eq('id', postId)
          .eq('user_id', currentUser.id);

      return true;
    } catch (e) {
      throw Exception('Failed to delete post: $e');
    }
  }

  /// Get liked post IDs for a user in batch
  Future<Set<String>> _getLikedPostIds(
    String userId,
    List<String> postIds,
  ) async {
    try {
      if (postIds.isEmpty) {
        developer.log('PostsService: No post IDs provided for like check');
        return <String>{};
      }

      developer.log(
        'PostsService: Checking like status for ${postIds.length} posts for user: $userId',
      );

      final response = await _client
          .from('likes')
          .select('post_id')
          .eq('user_id', userId)
          .inFilter('post_id', postIds);

      final List<dynamic> data = response as List<dynamic>;
      final likedPostIds =
          data
              .map((json) => json['post_id'] as String)
              .where((postId) => postId.isNotEmpty)
              .toSet();

      developer.log(
        'PostsService: Found ${likedPostIds.length} liked posts: $likedPostIds',
      );
      return likedPostIds;
    } catch (e) {
      developer.log('PostsService: Error fetching liked post IDs: $e');
      if (kDebugMode) {
        print('PostsService LIKED POST IDS ERROR: $e');
      }
      return <String>{};
    }
  }

  /// Get user's liked posts with full post information
  Future<List<LikeModel>> getUserLikedPosts(
    String userId, {
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _client
          .from('likes')
          .select('''
            id,
            user_id,
            post_id,
            created_at,
            posts!inner(
              id,
              content,
              media_url,
              media_type,
              like_count,
              comment_count,
              created_at,
              users!inner(
                username,
                display_name,
                avatar_url
              )
            )
          ''')
          .eq('user_id', userId)
          .not('post_id', 'is', null)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      final List<dynamic> data = response as List<dynamic>;

      return data.map((json) {
        final postInfo = json['posts'] as Map<String, dynamic>?;
        final userInfo = postInfo?['users'] as Map<String, dynamic>?;

        return LikeModel.fromJson({
          'id': json['id'],
          'user_id': json['user_id'],
          'post_id': json['post_id'],
          'created_at': json['created_at'],
          'username': userInfo?['username'],
          'display_name': userInfo?['display_name'],
          'avatar_url': userInfo?['avatar_url'],
          'post_content': postInfo?['content'],
          'post_media_url': postInfo?['media_url'],
        });
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch user liked posts: $e');
    }
  }

  /// Get posts liked by current user
  Future<List<LikeModel>> getCurrentUserLikedPosts({
    int limit = 20,
    int offset = 0,
  }) async {
    final currentUser = _client.auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not authenticated');
    }
    return getUserLikedPosts(currentUser.id, limit: limit, offset: offset);
  }
}
