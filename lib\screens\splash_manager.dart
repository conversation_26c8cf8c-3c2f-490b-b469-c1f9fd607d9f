import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:gameflex_mobile/screens/login_screen.dart';
import 'package:gameflex_mobile/screens/home_screen.dart';
import 'package:gameflex_mobile/providers/auth_provider.dart';

class SplashManager extends StatefulWidget {
  const SplashManager({super.key});

  @override
  State<SplashManager> createState() => _SplashManagerState();
}

class _SplashManagerState extends State<SplashManager> {
  bool _authTimeout = false;

  @override
  void initState() {
    super.initState();

    // Start a timeout for auth loading
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _authTimeout = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Show the main app with auth logic (built-in splash screen will show first)
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        // If auth is taking too long, force to login screen
        if (_authTimeout && (authProvider.status == AuthStatus.initial || authProvider.status == AuthStatus.loading)) {
          return const LoginScreen();
        }

        switch (authProvider.status) {
          case AuthStatus.initial:
          case AuthStatus.loading:
            return Scaffold(
              backgroundColor: Colors.grey[900], // Match built-in splash background
              body: const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            );
          case AuthStatus.authenticated:
            return const HomeScreen();
          case AuthStatus.unauthenticated:
          case AuthStatus.error:
            return const LoginScreen();
        }
      },
    );
  }
}
